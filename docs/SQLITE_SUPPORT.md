# SQLite 数据库支持指南

AI-BI 智能商业分析系统现在支持 SQLite 数据库作为主数据库的替代选择，特别适用于开发、测试和轻量级部署场景。

## 🌟 功能特性

### 双数据库支持
- **PostgreSQL**: 默认的生产级数据库，适用于高并发和大数据量场景
- **SQLite**: 轻量级数据库，适用于开发、测试和小规模部署

### 动态切换
- 通过环境变量配置数据库类型
- 无需修改代码即可在两种数据库间切换
- 提供便捷的切换脚本和工具

### 完整功能支持
- 数据源管理（支持 SQLite 作为外部数据源）
- 元数据提取和同步
- 关联关系发现
- 对话历史记录
- 所有核心 API 功能

## 🚀 快速开始

### 方法一：使用启动脚本切换

```bash
# 启动项目并选择数据库切换选项
./start.sh

# 在菜单中选择 "4) 切换数据库类型"
# 然后选择 "2) SQLite (轻量级)"
```

### 方法二：使用命令行切换

```bash
# 切换到 SQLite
cd backend
npm run switch-db sqlite

# 切换回 PostgreSQL
npm run switch-db postgresql
```

### 方法三：手动配置

1. **修改环境变量文件**
```bash
# 编辑 backend/.env 文件
DATABASE_PROVIDER="sqlite"
DATABASE_URL="file:./data/ai-bi.sqlite"
```

2. **初始化数据库**
```bash
cd backend
npm run db:init
npm run db:push
```

## 📋 配置说明

### 环境变量配置

```bash
# 数据库提供者类型
DATABASE_PROVIDER="sqlite"  # 或 "postgresql"

# SQLite 配置
DATABASE_URL="file:./data/ai-bi.sqlite"

# PostgreSQL 配置（当 DATABASE_PROVIDER=postgresql 时使用）
# DATABASE_URL="postgresql://user:password@localhost:5432/database"
```

### SQLite 文件路径

- **相对路径**: `file:./data/ai-bi.sqlite`
- **绝对路径**: `file:/absolute/path/to/database.sqlite`
- **内存数据库**: `file::memory:` (仅用于测试)

## 🔧 开发指南

### 添加 SQLite 作为外部数据源

```typescript
import { DataSourceService } from './services/dataSourceService';
import { SQLiteConnectionConfig } from './types';

const dataSourceService = new DataSourceService(prisma);

const config: SQLiteConnectionConfig = {
  filePath: '/path/to/your/database.sqlite',
  mode: 'readonly' // 或 'readwrite', 'create'
};

const dataSourceId = await dataSourceService.createDataSource(
  'My SQLite Database',
  'sqlite',
  config
);
```

### SQLite 连接模式

- **readonly**: 只读模式，适用于数据分析
- **readwrite**: 读写模式，适用于数据修改
- **create**: 创建模式，如果文件不存在则创建

### 数据类型映射

| PostgreSQL | SQLite | 说明 |
|------------|--------|------|
| UUID | TEXT | 使用 CUID 替代 UUID |
| TIMESTAMPTZ | DATETIME | 时间戳 |
| JSONB | TEXT | JSON 数据存储为文本 |
| VARCHAR(n) | TEXT | 文本类型 |
| BOOLEAN | INTEGER | 0/1 表示 false/true |

## 🐳 Docker 部署

### 使用 SQLite 的 Docker 配置

```yaml
# docker-compose.yml
services:
  backend:
    environment:
      - DATABASE_PROVIDER=sqlite
      - DATABASE_URL=file:/app/data/ai-bi.sqlite
    volumes:
      - sqlite_data:/app/data

volumes:
  sqlite_data:
```

### 数据持久化

SQLite 数据文件存储在 Docker 卷中，确保数据在容器重启后保持不变。

## 🧪 测试

### 运行 SQLite 相关测试

```bash
cd backend

# 运行所有测试
npm test

# 运行 SQLite 特定测试
npm test -- --testNamePattern="SQLite"

# 运行集成测试
npm test -- src/__tests__/integration/sqlite-workflow.test.ts
```

### 测试覆盖范围

- SQLite 连接测试
- 元数据提取测试
- 数据源管理测试
- 完整工作流程集成测试

## ⚠️ 限制和注意事项

### SQLite 限制

1. **并发限制**: SQLite 不支持高并发写操作
2. **网络访问**: SQLite 是文件数据库，不支持网络连接
3. **数据类型**: 类型系统相对简单
4. **大小限制**: 适用于中小型数据集（< 1GB）

### 使用建议

- **开发环境**: 推荐使用 SQLite，简化环境搭建
- **测试环境**: 适用于单元测试和集成测试
- **生产环境**: 小规模部署可考虑，大规模建议使用 PostgreSQL
- **数据分析**: 适用于分析现有的 SQLite 数据文件

## 🔄 数据迁移

### 从 PostgreSQL 迁移到 SQLite

```bash
# 1. 导出 PostgreSQL 数据
pg_dump -h localhost -U user -d database --data-only --inserts > data.sql

# 2. 切换到 SQLite
npm run switch-db sqlite

# 3. 手动调整 SQL 语句以兼容 SQLite
# 4. 导入数据到 SQLite
```

### 从 SQLite 迁移到 PostgreSQL

```bash
# 1. 切换到 PostgreSQL
npm run switch-db postgresql

# 2. 使用工具导出 SQLite 数据
# 3. 调整数据格式以兼容 PostgreSQL
# 4. 导入数据
```

## 📚 相关文档

- [Prisma SQLite 文档](https://www.prisma.io/docs/concepts/database-connectors/sqlite)
- [SQLite 官方文档](https://www.sqlite.org/docs.html)
- [项目部署指南](./DEPLOYMENT.md)
- [API 文档](./API.md)

## 🆘 故障排除

### 常见问题

1. **文件权限错误**
   ```bash
   # 确保应用有读写权限
   chmod 664 /path/to/database.sqlite
   ```

2. **数据库锁定**
   ```bash
   # 检查是否有其他进程在使用数据库
   lsof /path/to/database.sqlite
   ```

3. **迁移失败**
   ```bash
   # 重新生成 Prisma 客户端
   npm run db:generate
   npm run db:push
   ```

### 获取帮助

如果遇到问题，请：
1. 查看应用日志
2. 检查数据库文件权限
3. 验证环境变量配置
4. 提交 Issue 并附上错误信息
