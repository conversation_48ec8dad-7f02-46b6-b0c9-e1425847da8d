// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// 默认使用 PostgreSQL 配置
// 使用切换脚本可以自动替换为 SQLite 配置
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 数据源表
model DataSource {
  id               String   @id @default(cuid()) // 使用 cuid() 替代 uuid() 以兼容 SQLite
  name             String   // 移除 PostgreSQL 特定的 @db 属性以兼容 SQLite
  type             String   // postgresql, mysql, csv, excel, sqlite
  connectionConfig String   // 加密后的连接配置信息 (JSON 格式)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // 关联关系
  dataTables DataTable[]

  @@map("data_sources")
}

// 数据表元数据表
model DataTable {
  id                String   @id @default(cuid())
  dataSourceId      String   @map("data_source_id")
  originalTableName String   @map("original_table_name")
  aliasName         String?  @map("alias_name")
  description       String?
  isSynced          Boolean  @default(false) @map("is_synced")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // 关联关系
  dataSource  DataSource   @relation(fields: [dataSourceId], references: [id], onDelete: Cascade)
  dataColumns DataColumn[]

  @@map("data_tables")
}

// 数据列元数据表
model DataColumn {
  id                 String   @id @default(cuid())
  dataTableId        String   @map("data_table_id")
  originalColumnName String   @map("original_column_name")
  originalDataType   String   @map("original_data_type")
  aliasName          String?  @map("alias_name")
  description        String?
  isPrimaryKey       Boolean  @default(false) @map("is_primary_key")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  // 关联关系
  dataTable DataTable @relation(fields: [dataTableId], references: [id], onDelete: Cascade)

  // 作为关联关系的起始列
  fromRelationships DataRelationship[] @relation("FromColumn")
  // 作为关联关系的目标列
  toRelationships   DataRelationship[] @relation("ToColumn")

  @@map("data_columns")
}

// 数据关联关系表
model DataRelationship {
  id               String   @id @default(cuid())
  fromColumnId     String   @map("from_column_id")
  toColumnId       String   @map("to_column_id")
  relationshipType String   @map("relationship_type") // one_to_one, one_to_many, many_to_one
  isManual         Boolean  @default(false) @map("is_manual") // 是否为用户手动创建

  // 置信度相关字段
  discoveryMethod  String?  @map("discovery_method") // foreign_key, naming_convention, data_analysis, manual
  confidence       String?  @map("confidence") // high, medium, low
  confidenceScore  Float?   @map("confidence_score") // 0-1之间的数值
  evidence         String   @map("evidence") // 支持该关联关系的证据，SQLite 不支持数组，使用 JSON 字符串存储
  metadata         String?  @map("metadata") // 额外的元数据信息，SQLite 不支持 Json 类型，使用字符串存储

  createdAt        DateTime @default(now()) @map("created_at")

  // 关联关系
  fromColumn DataColumn @relation("FromColumn", fields: [fromColumnId], references: [id], onDelete: Cascade)
  toColumn   DataColumn @relation("ToColumn", fields: [toColumnId], references: [id], onDelete: Cascade)

  @@map("data_relationships")
}

// 对话历史记录表
model ChatHistory {
  id                       String   @id @default(cuid())
  sessionId                String   @map("session_id")
  userQuery                String   @map("user_query")
  generatedSql             String?  @map("generated_sql")
  queryResultData          String?  @map("query_result_data") // SQLite 不支持 Json 类型，使用字符串存储
  naturalLanguageResponse  String?  @map("natural_language_response")
  visualizationSpec        String?  @map("visualization_spec") // SQLite 不支持 Json 类型，使用字符串存储
  createdAt                DateTime @default(now()) @map("created_at")

  @@map("chat_history")
}
