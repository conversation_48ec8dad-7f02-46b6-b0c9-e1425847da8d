/**
 * SQLite 支持功能测试
 */

import { DataSourceService } from '../services/dataSourceService';
import { MetadataService } from '../services/metadataService';
import { validateDataSourceConfig } from '../utils/validation';
import { SQLiteConnectionConfig } from '../types';
import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

describe('SQLite 支持测试', () => {
  let prisma: PrismaClient;
  let dataSourceService: DataSourceService;
  let metadataService: MetadataService;
  let testDbPath: string;

  beforeAll(() => {
    // 创建测试数据库路径
    testDbPath = path.join(__dirname, '../../../test-data/test.sqlite');
    const testDir = path.dirname(testDbPath);
    
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    prisma = new PrismaClient();
    dataSourceService = new DataSourceService(prisma);
    metadataService = new MetadataService(prisma);
  });

  afterAll(async () => {
    await prisma.$disconnect();
    
    // 清理测试文件
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
    }
  });

  describe('SQLite 配置验证', () => {
    test('应该验证有效的 SQLite 配置', () => {
      const config: SQLiteConnectionConfig = {
        filePath: testDbPath,
        mode: 'create'
      };

      expect(() => {
        validateDataSourceConfig('sqlite', config);
      }).not.toThrow();
    });

    test('应该拒绝无效的 SQLite 配置', () => {
      const invalidConfig = {
        // 缺少 filePath
        mode: 'create'
      };

      expect(() => {
        validateDataSourceConfig('sqlite', invalidConfig);
      }).toThrow('SQLite 文件路径不能为空');
    });

    test('应该验证 SQLite 模式选项', () => {
      const config: SQLiteConnectionConfig = {
        filePath: testDbPath,
        mode: 'invalid' as any
      };

      expect(() => {
        validateDataSourceConfig('sqlite', config);
      }).toThrow('SQLite 打开模式必须是 readonly, readwrite 或 create 之一');
    });
  });

  describe('SQLite 连接测试', () => {
    test('应该能够测试 SQLite 连接', async () => {
      const config: SQLiteConnectionConfig = {
        filePath: testDbPath,
        mode: 'create'
      };

      const result = await dataSourceService.testConnection('sqlite', config);
      expect(result).toBe(true);
    });

    test('应该处理无效的 SQLite 文件路径', async () => {
      const config: SQLiteConnectionConfig = {
        filePath: '/invalid/path/test.sqlite',
        mode: 'readonly'
      };

      await expect(
        dataSourceService.testConnection('sqlite', config)
      ).rejects.toThrow();
    });
  });

  describe('SQLite 元数据提取', () => {
    test('应该能够提取空 SQLite 数据库的元数据', async () => {
      // 创建一个空的 SQLite 数据库
      const sqlite3 = require('sqlite3');
      const db = new sqlite3.Database(testDbPath);
      
      await new Promise<void>((resolve, reject) => {
        db.close((err: any) => {
          if (err) reject(err);
          else resolve();
        });
      });

      const config: SQLiteConnectionConfig = {
        filePath: testDbPath,
        mode: 'readonly'
      };

      // 使用私有方法进行测试（需要类型断言）
      const metadata = await (metadataService as any).extractSQLiteMetadata(config);
      
      expect(metadata).toHaveProperty('tables');
      expect(Array.isArray(metadata.tables)).toBe(true);
    });

    test('应该能够提取包含表的 SQLite 数据库元数据', async () => {
      // 创建包含测试表的 SQLite 数据库
      const sqlite3 = require('sqlite3');
      const db = new sqlite3.Database(testDbPath);
      
      await new Promise<void>((resolve, reject) => {
        db.serialize(() => {
          db.run(`CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )`);
          
          db.run(`CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            amount REAL,
            status TEXT DEFAULT 'pending',
            FOREIGN KEY (user_id) REFERENCES users (id)
          )`);
          
          db.close((err: any) => {
            if (err) reject(err);
            else resolve();
          });
        });
      });

      const config: SQLiteConnectionConfig = {
        filePath: testDbPath,
        mode: 'readonly'
      };

      const metadata = await (metadataService as any).extractSQLiteMetadata(config);
      
      expect(metadata.tables).toHaveLength(2);
      
      const usersTable = metadata.tables.find((t: any) => t.name === 'users');
      expect(usersTable).toBeDefined();
      expect(usersTable.columns).toHaveLength(4);
      
      const idColumn = usersTable.columns.find((c: any) => c.name === 'id');
      expect(idColumn.isPrimaryKey).toBe(true);
      expect(idColumn.dataType).toBe('INTEGER');
      
      const ordersTable = metadata.tables.find((t: any) => t.name === 'orders');
      expect(ordersTable).toBeDefined();
      expect(ordersTable.columns).toHaveLength(4);
    });
  });

  describe('数据源类型支持', () => {
    test('应该在支持的数据源类型中包含 sqlite', () => {
      const { DataSourceType } = require('../types');
      
      // 这个测试确保 TypeScript 类型定义正确
      const sqliteType: typeof DataSourceType = 'sqlite';
      expect(sqliteType).toBe('sqlite');
    });
  });
});
