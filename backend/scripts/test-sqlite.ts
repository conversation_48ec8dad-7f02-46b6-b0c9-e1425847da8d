#!/usr/bin/env ts-node

/**
 * SQLite 功能验证脚本
 * 测试 SQLite 数据库的基本功能
 */

import { PrismaClient } from '@prisma/client';
import { DataSourceService } from '../src/services/dataSourceService';
import { MetadataService } from '../src/services/metadataService';
import { SQLiteConnectionConfig } from '../src/types';
import sqlite3 from 'sqlite3';
import fs from 'fs';
import path from 'path';

async function createTestSQLiteDatabase(): Promise<string> {
  const testDbPath = path.join(__dirname, '../test-data/verification.sqlite');
  const testDir = path.dirname(testDbPath);
  
  // 确保目录存在
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  // 删除现有文件
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(testDbPath);
    
    db.serialize(() => {
      // 创建测试表
      db.run(`CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE,
        phone TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      db.run(`CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        price REAL NOT NULL,
        category TEXT,
        stock INTEGER DEFAULT 0
      )`);

      db.run(`CREATE TABLE orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER,
        total_amount REAL,
        order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )`);

      // 插入测试数据
      db.run(`INSERT INTO customers (name, email, phone) VALUES 
        ('张三', '<EMAIL>', '13800138001'),
        ('李四', '<EMAIL>', '13800138002'),
        ('王五', '<EMAIL>', '13800138003')`);

      db.run(`INSERT INTO products (name, price, category, stock) VALUES 
        ('笔记本电脑', 5999.99, '电子产品', 10),
        ('无线鼠标', 99.99, '电子产品', 50),
        ('办公椅', 899.99, '办公用品', 20)`);

      db.run(`INSERT INTO orders (customer_id, total_amount) VALUES 
        (1, 6099.98),
        (2, 99.99),
        (3, 1799.98)`);

      db.close((err) => {
        if (err) reject(err);
        else resolve(testDbPath);
      });
    });
  });
}

async function testSQLiteIntegration() {
  console.log('🧪 开始 SQLite 功能验证...');

  let prisma: PrismaClient | null = null;
  let dataSourceService: DataSourceService | null = null;
  let dataSourceId: string | null = null;

  try {
    // 1. 创建测试数据库
    console.log('📊 创建测试 SQLite 数据库...');
    const testDbPath = await createTestSQLiteDatabase();
    console.log(`✅ 测试数据库创建成功: ${testDbPath}`);

    // 2. 初始化服务
    prisma = new PrismaClient();
    dataSourceService = new DataSourceService(prisma);
    const metadataService = new MetadataService(prisma);

    // 3. 测试 SQLite 连接
    console.log('🔗 测试 SQLite 连接...');
    const config: SQLiteConnectionConfig = {
      filePath: testDbPath,
      mode: 'readwrite'
    };

    const connectionResult = await dataSourceService.testConnection('sqlite', config);
    console.log(`✅ SQLite 连接测试: ${connectionResult ? '成功' : '失败'}`);

    // 4. 创建数据源
    console.log('📝 创建 SQLite 数据源...');
    dataSourceId = await dataSourceService.createDataSource(
      'SQLite 验证数据库',
      'sqlite',
      config
    );
    console.log(`✅ 数据源创建成功: ${dataSourceId}`);

    // 5. 验证数据源列表
    console.log('📋 获取数据源列表...');
    const dataSources = await dataSourceService.getDataSources();
    const createdDataSource = dataSources.find(ds => ds.id === dataSourceId);
    
    if (createdDataSource) {
      console.log(`✅ 数据源验证成功:`);
      console.log(`   - 名称: ${createdDataSource.name}`);
      console.log(`   - 类型: ${createdDataSource.type}`);
      console.log(`   - 状态: ${createdDataSource.status}`);
    } else {
      throw new Error('数据源未找到');
    }

    // 6. 测试元数据提取
    console.log('🔍 测试元数据提取...');
    const metadata = await (metadataService as any).extractSQLiteMetadata(config);
    
    console.log(`✅ 元数据提取成功:`);
    console.log(`   - 表数量: ${metadata.tables.length}`);
    
    metadata.tables.forEach((table: any) => {
      console.log(`   - 表: ${table.name} (${table.columns.length} 列)`);
    });

    // 7. 测试连接状态检查
    console.log('🔍 检查连接状态...');
    const connectionStatus = await dataSourceService.checkConnectionStatus(dataSourceId);
    console.log(`✅ 连接状态: ${connectionStatus}`);

    console.log('\n🎉 SQLite 功能验证完成！所有测试通过。');
    
    return true;

  } catch (error) {
    console.error('❌ SQLite 功能验证失败:', error);
    return false;
    
  } finally {
    // 清理资源
    if (dataSourceId && dataSourceService) {
      try {
        await dataSourceService.deleteDataSource(dataSourceId);
        console.log('🧹 清理测试数据源完成');
      } catch (error) {
        console.warn('⚠️  清理数据源失败:', error);
      }
    }
    
    if (prisma) {
      await prisma.$disconnect();
    }
  }
}

async function main() {
  console.log('🚀 SQLite 功能验证脚本');
  console.log('========================');
  
  const success = await testSQLiteIntegration();
  
  if (success) {
    console.log('\n✅ 验证成功！SQLite 支持功能正常工作。');
    process.exit(0);
  } else {
    console.log('\n❌ 验证失败！请检查错误信息。');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}
