#!/usr/bin/env ts-node

/**
 * 数据库切换脚本
 * 用于在 PostgreSQL 和 SQLite 之间切换
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

interface DatabaseConfig {
  provider: 'postgresql' | 'sqlite';
  url: string;
}

const configs: Record<string, DatabaseConfig> = {
  postgresql: {
    provider: 'postgresql',
    url: 'postgresql://ai_bi_user:ai_bi_password@localhost:5432/ai_bi_system'
  },
  sqlite: {
    provider: 'sqlite',
    url: 'file:./data/ai-bi.sqlite'
  }
};

/**
 * 更新环境变量文件
 */
function updateEnvFile(config: DatabaseConfig) {
  const envPath = path.join(__dirname, '../.env');
  const envExamplePath = path.join(__dirname, '../.env.example');
  
  // 如果 .env 文件不存在，从 .env.example 复制
  if (!fs.existsSync(envPath) && fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    console.log('📝 从 .env.example 创建 .env 文件');
  }
  
  if (fs.existsSync(envPath)) {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // 更新 DATABASE_PROVIDER
    envContent = envContent.replace(
      /DATABASE_PROVIDER=.*/,
      `DATABASE_PROVIDER="${config.provider}"`
    );
    
    // 更新 DATABASE_URL
    envContent = envContent.replace(
      /DATABASE_URL=.*/,
      `DATABASE_URL="${config.url}"`
    );
    
    fs.writeFileSync(envPath, envContent);
    console.log(`✅ 更新 .env 文件: ${config.provider}`);
  } else {
    // 创建新的 .env 文件
    const envContent = `DATABASE_PROVIDER="${config.provider}"
DATABASE_URL="${config.url}"
REDIS_URL="redis://localhost:6379"
PORT=3001
NODE_ENV=development
`;
    fs.writeFileSync(envPath, envContent);
    console.log(`✅ 创建 .env 文件: ${config.provider}`);
  }
}

/**
 * 切换 Prisma schema 文件
 */
function switchSchema(provider: 'postgresql' | 'sqlite') {
  const schemaPath = path.join(__dirname, '../prisma/schema.prisma');
  const postgresSchemaPath = path.join(__dirname, '../prisma/schema.postgresql.prisma');
  const sqliteSchemaPath = path.join(__dirname, '../prisma/schema.sqlite.prisma');

  // 如果是第一次切换，备份原始的 PostgreSQL schema
  if (!fs.existsSync(postgresSchemaPath) && provider === 'sqlite') {
    fs.copyFileSync(schemaPath, postgresSchemaPath);
    console.log('📝 备份 PostgreSQL schema 文件');
  }

  if (provider === 'sqlite') {
    if (fs.existsSync(sqliteSchemaPath)) {
      fs.copyFileSync(sqliteSchemaPath, schemaPath);
      console.log('✅ 切换到 SQLite schema');
    } else {
      throw new Error('SQLite schema 文件不存在');
    }
  } else {
    if (fs.existsSync(postgresSchemaPath)) {
      fs.copyFileSync(postgresSchemaPath, schemaPath);
      console.log('✅ 切换到 PostgreSQL schema');
    } else {
      // 恢复默认的 PostgreSQL 配置
      const defaultSchema = `// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// 默认使用 PostgreSQL 配置
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}`;
      fs.writeFileSync(schemaPath, defaultSchema);
      console.log('✅ 恢复默认 PostgreSQL schema');
    }
  }
}

/**
 * 运行数据库迁移
 */
function runMigration() {
  try {
    console.log('🔄 生成 Prisma 客户端...');
    execSync('npx prisma generate', { stdio: 'inherit', cwd: path.join(__dirname, '..') });

    console.log('🔄 推送数据库架构...');
    execSync('npx prisma db push', { stdio: 'inherit', cwd: path.join(__dirname, '..') });

    console.log('✅ 数据库迁移完成');
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    throw error;
  }
}

/**
 * 确保 SQLite 数据目录存在
 */
function ensureSQLiteDataDir() {
  const dataDir = path.join(__dirname, '../data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    console.log(`✅ 创建数据目录: ${dataDir}`);
  }
}

/**
 * 主函数
 */
async function main() {
  const targetDb = process.argv[2];
  
  if (!targetDb || !configs[targetDb]) {
    console.log('使用方法: npm run switch-db <postgresql|sqlite>');
    console.log('示例:');
    console.log('  npm run switch-db postgresql  # 切换到 PostgreSQL');
    console.log('  npm run switch-db sqlite      # 切换到 SQLite');
    process.exit(1);
  }
  
  const config = configs[targetDb];
  
  console.log(`🔄 切换数据库到: ${config.provider.toUpperCase()}`);
  
  try {
    // 如果切换到 SQLite，确保数据目录存在
    if (config.provider === 'sqlite') {
      ensureSQLiteDataDir();
    }
    
    // 更新环境变量
    updateEnvFile(config);

    // 切换 schema 文件
    switchSchema(config.provider);

    // 运行迁移
    runMigration();
    
    console.log(`🎉 成功切换到 ${config.provider.toUpperCase()} 数据库`);
    console.log(`📍 数据库 URL: ${config.url}`);
    
  } catch (error) {
    console.error('❌ 数据库切换失败:', error);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}
