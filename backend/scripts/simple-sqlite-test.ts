#!/usr/bin/env ts-node

/**
 * 简化的 SQLite 功能验证脚本
 * 测试基本的 SQLite 连接和操作
 */

import sqlite3 from 'sqlite3';
import fs from 'fs';
import path from 'path';
import { validateDataSourceConfig } from '../src/utils/validation';
import { SQLiteConnectionConfig } from '../src/types';

async function createTestDatabase(): Promise<string> {
  const testDbPath = path.join(__dirname, '../test-data/simple-test.sqlite');
  const testDir = path.dirname(testDbPath);
  
  // 确保目录存在
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  // 删除现有文件
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(testDbPath);
    
    db.serialize(() => {
      // 创建测试表
      db.run(`CREATE TABLE test_users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      // 插入测试数据
      db.run(`INSERT INTO test_users (name, email) VALUES 
        ('测试用户1', '<EMAIL>'),
        ('测试用户2', '<EMAIL>')`);

      db.close((err) => {
        if (err) reject(err);
        else resolve(testDbPath);
      });
    });
  });
}

async function testSQLiteConnection(filePath: string): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(filePath, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      // 执行简单查询测试连接
      db.get('SELECT COUNT(*) as count FROM test_users', (err, row) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.warn('数据库关闭警告:', closeErr.message);
          }
        });

        if (err) {
          reject(err);
        } else {
          console.log(`✅ 查询结果: 找到 ${(row as any).count} 条记录`);
          resolve(true);
        }
      });
    });
  });
}

async function testSQLiteMetadata(filePath: string): Promise<any> {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(filePath, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      // 获取表信息
      db.all(`
        SELECT name, sql 
        FROM sqlite_master 
        WHERE type = 'table' 
        AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `, (err, tables) => {
        if (err) {
          db.close();
          reject(err);
          return;
        }

        const tablePromises = (tables as any[]).map(table => {
          return new Promise((resolveTable, rejectTable) => {
            // 获取表的列信息
            db.all(`PRAGMA table_info(${table.name})`, (err, columns) => {
              if (err) {
                rejectTable(err);
                return;
              }

              const tableColumns = (columns as any[]).map(column => ({
                name: column.name,
                dataType: column.type || 'TEXT',
                isNullable: column.notnull === 0,
                defaultValue: column.dflt_value,
                isPrimaryKey: column.pk === 1
              }));

              resolveTable({
                name: table.name,
                comment: `SQLite 表: ${table.name}`,
                columns: tableColumns
              });
            });
          });
        });

        Promise.all(tablePromises)
          .then(tablesData => {
            db.close((closeErr) => {
              if (closeErr) {
                console.warn('数据库关闭警告:', closeErr.message);
              }
            });
            
            resolve({ tables: tablesData });
          })
          .catch(error => {
            db.close();
            reject(error);
          });
      });
    });
  });
}

async function main() {
  console.log('🚀 简化 SQLite 功能验证');
  console.log('========================');
  
  try {
    // 1. 创建测试数据库
    console.log('📊 创建测试数据库...');
    const testDbPath = await createTestDatabase();
    console.log(`✅ 测试数据库创建成功: ${testDbPath}`);

    // 2. 测试配置验证
    console.log('🔍 测试配置验证...');
    const config: SQLiteConnectionConfig = {
      filePath: testDbPath,
      mode: 'readonly'
    };

    try {
      validateDataSourceConfig('sqlite', config);
      console.log('✅ 配置验证通过');
    } catch (error) {
      throw new Error(`配置验证失败: ${error}`);
    }

    // 3. 测试数据库连接
    console.log('🔗 测试数据库连接...');
    const connectionResult = await testSQLiteConnection(testDbPath);
    console.log(`✅ 连接测试: ${connectionResult ? '成功' : '失败'}`);

    // 4. 测试元数据提取
    console.log('🔍 测试元数据提取...');
    const metadata = await testSQLiteMetadata(testDbPath);
    
    console.log(`✅ 元数据提取成功:`);
    console.log(`   - 表数量: ${metadata.tables.length}`);
    
    metadata.tables.forEach((table: any) => {
      console.log(`   - 表: ${table.name} (${table.columns.length} 列)`);
      table.columns.forEach((column: any) => {
        const pkFlag = column.isPrimaryKey ? ' [PK]' : '';
        console.log(`     - ${column.name}: ${column.dataType}${pkFlag}`);
      });
    });

    // 5. 测试无效配置
    console.log('🧪 测试错误处理...');
    try {
      const invalidConfig = {
        filePath: '/nonexistent/path.sqlite',
        mode: 'readonly'
      };
      await testSQLiteConnection(invalidConfig.filePath);
      console.log('❌ 错误处理测试失败：应该抛出错误');
    } catch (error) {
      console.log('✅ 错误处理测试通过：正确捕获了无效路径错误');
    }

    console.log('\n🎉 所有测试通过！SQLite 支持功能正常工作。');
    
    // 清理测试文件
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
      console.log('🧹 清理测试文件完成');
    }
    
    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  main().then(success => {
    if (success) {
      console.log('\n✅ 验证成功！');
      process.exit(0);
    } else {
      console.log('\n❌ 验证失败！');
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}
