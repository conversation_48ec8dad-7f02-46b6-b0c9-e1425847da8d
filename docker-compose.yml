version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: ai-bi-postgres
    environment:
      POSTGRES_DB: ai_bi_system
      POSTGRES_USER: ai_bi_user
      POSTGRES_PASSWORD: ai_bi_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - ai-bi-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ai-bi-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-bi-network

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-bi-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_PROVIDER=postgresql  # 可以改为 sqlite 来使用 SQLite
      - DATABASE_URL=****************************************************/ai_bi_system
      # SQLite 配置示例 (当 DATABASE_PROVIDER=sqlite 时使用):
      # - DATABASE_URL=file:/app/data/ai-bi.sqlite
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
      - sqlite_data:/app/data  # SQLite 数据持久化目录
    networks:
      - ai-bi-network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai-bi-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - ai-bi-network

volumes:
  postgres_data:
  redis_data:
  sqlite_data:  # SQLite 数据持久化卷

networks:
  ai-bi-network:
    driver: bridge
