# AI-BI 智能商业分析系统

一个基于人工智能的商业智能(BI)数据管理系统，通过自然语言处理技术简化数据集成、分析与可视化，提供直观、高效的用户体验。

## 🌟 核心特性

### 📊 数据源管理
- **多数据源支持**: 支持 PostgreSQL、MySQL、SQLite、CSV 文件、Excel 文件
- **双数据库架构**: 支持 PostgreSQL（生产级）和 SQLite（轻量级）作为主数据库
- **连接状态监控**: 实时监控数据源连接状态
- **元数据同步**: 自动同步数据表结构和字段信息
- **安全连接**: 加密存储数据库连接信息

### 🧠 语义层配置
- **业务别名**: 为数据表和字段设置易于理解的业务名称
- **关联关系管理**: 可视化管理表之间的关联关系
- **自动发现**: 基于外键约束和命名约定自动发现关联关系
- **跨数据源关联**: 支持不同数据源之间建立关联关系

### 💬 自然语言交互
- **对话式查询**: 使用自然语言提问进行数据查询
- **智能SQL生成**: AI自动将自然语言转换为SQL查询
- **多轮对话**: 支持上下文理解的连续对话
- **查询历史**: 保存和回顾历史查询记录

### 📈 智能可视化
- **自动图表生成**: 根据查询结果自动推荐最合适的图表类型
- **多种图表类型**: 支持柱状图、折线图、饼图、表格等
- **交互式图表**: 支持图表交互和数据钻取
- **图表导出**: 支持图表和数据导出

## 🗄️ 数据库支持

### 主数据库选项
- **PostgreSQL** (默认): 生产级数据库，适用于高并发和大数据量场景
- **SQLite**: 轻量级数据库，适用于开发、测试和小规模部署

### 快速切换数据库
```bash
# 使用启动脚本切换
./start.sh  # 选择菜单中的数据库切换选项

# 或使用命令行切换
cd backend
npm run switch-db sqlite      # 切换到 SQLite
npm run switch-db postgresql  # 切换到 PostgreSQL
```

详细信息请参考 [SQLite 支持指南](docs/SQLITE_SUPPORT.md)